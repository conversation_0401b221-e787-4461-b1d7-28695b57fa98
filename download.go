package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"
)

// 计算当月第几周
func getWeekOfMonth(t time.Time) int {
	_, week := t.ISOWeek()
	currentMonth := t.Month()
	firstDay := time.Date(t.Year(), currentMonth, 1, 0, 0, 0, 0, t.Location())
	_, firstWeek := firstDay.ISOWeek()
	return week - firstWeek + 1
}

// 生成目标文件夹名
func generateFolderName(t time.Time) string {
	weekNum := getWeekOfMonth(t)
	return fmt.Sprintf("%d%02dW%d", t.Year(), t.Month(), weekNum)
}

// 验证IP地址格式
func isValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}
	for _, part := range parts {
		num, err := strconv.Atoi(part)
		if err != nil || num < 0 || num > 255 {
			return false
		}
	}
	return true
}

// 显示使用说明
func showUsage() {
	log.Println("Usage:")
	log.Println("  ./download.go                           - Use default values")
	log.Println("  ./download.go <ip_address>              - Set server IP")
	log.Println("  ./download.go --local                    - Disable remote operations (local mode)")
	log.Println("  ./download.go --local [other_args]       - Local mode (other arguments ignored)")
	log.Println("")
	log.Println("Examples:")
	log.Println("  ./download.go *************")
	log.Println("  ./download.go --local")
	log.Println("  ./download.go --local *************      - IP ignored in local mode")
}

// 解析命令行参数
func parseArgs() (string, bool, error) {
	args := os.Args[1:] // 排除程序名
	defaultIP := "************"
	defaultRemoteEnable := true

	// 首先检查是否包含 --local 标志
	hasLocalFlag := slices.Contains(args, "--local")

	// 如果包含 --local 标志，忽略其他参数，设置 remote_enable = false
	if hasLocalFlag {
		log.Printf("Local mode enabled: remote operations disabled")
		return defaultIP, false, nil
	}

	// 过滤掉 --local 标志后的参数（虽然上面已经处理了，但保持代码清晰）
	var filteredArgs []string
	for _, arg := range args {
		if arg != "--local" {
			filteredArgs = append(filteredArgs, arg)
		}
	}
	args = filteredArgs

	switch len(args) {
	case 0:
		// 无参数，使用默认值
		return defaultIP, defaultRemoteEnable, nil

	case 1:
		// 一个参数，自动检测类型
		arg := args[0]
		if isValidIP(arg) {
			return arg, defaultRemoteEnable, nil
		}
		return "", false, fmt.Errorf("invalid argument '%s'. Expected IP address (x.x.x.x)", arg)

	default:
		return "", false, fmt.Errorf("too many arguments (%d). Expected 0-1 arguments", len(args))
	}
}

// 查找FTP目录下的最新文件
func findLatestFile() (latestFile string, err error) {

	cmd := exec.Command("winscp.com", "/command",
		"open *****************************************",
		"cd /rd-it-temp-out/xKF7490/developmentVersion/",
		"ls",
		"exit")

	output, cmdErr := cmd.CombinedOutput()
	if cmdErr != nil {
		log.Printf("Failed to list files: %v\nOutput: %s", cmdErr, output)
		return "", fmt.Errorf("failed to list files: %v", cmdErr)
	}

	// 解析输出找到最新文件
	files := strings.Split(string(output), "\n")
	var latestTime time.Time

	re := regexp.MustCompile(`plugins(\d{14})\.zip`)

	for _, file := range files {
		if matches := re.FindStringSubmatch(file); len(matches) > 1 {
			timeStr := matches[1]
			fileTime, err := time.ParseInLocation("20060102150405", timeStr, time.Local)
			if err != nil {
				continue
			}

			if latestTime.IsZero() || fileTime.After(latestTime) {
				latestTime = fileTime
				latestFile = matches[0]
			}
		}
	}

	if latestFile != "" {
		return latestFile, nil
	} else {
		log.Printf("No matching plugin files found in remote directory")
		return "", fmt.Errorf("no matching plugin files found in remote directory")
	}
}

// 执行下载和上传的核心业务逻辑
func executeDownloadProcess(serverIP string, remote_enable bool) (latestFile string, err error) {
	if remote_enable {
		// 远程到服务器，停止服务
		log.Printf("Stopping remote service on server %s...", serverIP)

		// 使用单行命令执行远程服务停止
		cmd := exec.Command("winscp.com", "/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			"call systemctl stop fist",
			"exit")

		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Failed to execute command: %v\nOutput: %s", cmdErr, output)
			return "", fmt.Errorf("failed to stop remote service: %v", cmdErr)
		}

		log.Println("Remote service stopped.")
	}

	// 查找ftp目录下所有文件并找出最新的
	log.Println("Searching for latest file...")
	latestFile, err = findLatestFile()
	if err != nil {
		return "", err
	}
	log.Printf("Found latest file: %s", latestFile)

	// 创建目标文件夹
	now := time.Now()
	folderName := generateFolderName(now)
	targetDir := filepath.Join(`C:\Users\<USER>\workspace`, folderName)

	if mkdirErr := os.MkdirAll(targetDir, 0755); mkdirErr != nil {
		log.Printf("Failed to create directory: %v", mkdirErr)
		return latestFile, fmt.Errorf("failed to create directory: %v", mkdirErr)
	}

	log.Printf("Downloading to folder: %s", targetDir)

	// 下载文件
	cmd := exec.Command("winscp.com", "/command",
		"open *****************************************",
		"cd /rd-it-temp-out/xKF7490/developmentVersion/",
		fmt.Sprintf("get %s %s\\%s", latestFile, targetDir, latestFile),
		"exit")

	if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
		log.Printf("Failed to download file: %v\nOutput: %s", cmdErr, output)
		return latestFile, fmt.Errorf("failed to download file: %v", cmdErr)
	}

	log.Printf("Successfully downloaded %s to %s", latestFile, targetDir)

	// 解压文件到下载目录
	log.Println("Extracting file...")

	// 构建源文件完整路径
	zipPath := filepath.Join(targetDir, latestFile)

	// 使用7zip解压文件，-y表示对所有询问自动回答yes（覆盖）
	cmd = exec.Command("7z", "x", zipPath, "-o"+targetDir, "-y")

	if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
		log.Printf("Failed to extract file: %v\nOutput: %s", cmdErr, output)
		return latestFile, fmt.Errorf("failed to extract file: %v", cmdErr)
	}

	log.Printf("Successfully extracted %s to %s", latestFile, targetDir)

	if remote_enable {
		// 上传解压后的plugins文件夹到服务器，并开启服务
		log.Printf("Uploading plugins folder to remote server %s and starting service...", serverIP)

		// 构建plugins文件夹路径
		now := time.Now()
		folderName := generateFolderName(now)
		targetDir := filepath.Join(`C:\Users\<USER>\workspace`, folderName)
		pluginsPath := filepath.Join(targetDir, "plugins")

		// 检查plugins文件夹是否存在
		if _, statErr := os.Stat(pluginsPath); os.IsNotExist(statErr) {
			log.Printf("Plugins folder not found at: %s", pluginsPath)
			return latestFile, fmt.Errorf("plugins folder not found at: %s", pluginsPath)
		}
		// 执行上传命令
		uploadCommand := []string{
			"/command",
			fmt.Sprintf("open sftp://root:Password@_@%s/usr/local/appfist/AppFist_now/", serverIP),
			fmt.Sprintf("put %s /usr/local/appfist/AppFist_now/", pluginsPath),
			"/exit"}
		cmd := exec.Command("winscp.com ", uploadCommand...)
		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Failed to upload plugins folder: %v\nOutput: %s", cmdErr, output)
			return latestFile, fmt.Errorf("failed to upload plugins folder: %v", cmdErr)
		}
		log.Printf("Successfully uploaded plugins folder to %s", "/usr/local/appfist/AppFist_now/")

		// 执行启动服务命令
		startCommand := []string{"/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			"call systemctl restart fist",
			"exit"}

		cmd = exec.Command("winscp.com", startCommand...)
		if output, cmdErr := cmd.CombinedOutput(); cmdErr != nil {
			log.Printf("Failed to start service: %v\nOutput: %s", cmdErr, output)
			return latestFile, fmt.Errorf("failed to start service: %v", cmdErr)
		}
		log.Println("Successfully restarted service")
	}

	// 所有操作成功完成
	return latestFile, nil
}

// 使用winscp下载最新文件并更新到服务器上
func main() {
	// 解析命令行参数
	serverIP, remote_enable, err := parseArgs()
	if err != nil {
		log.Printf("Error: %v", err)
		showUsage()
		os.Exit(1)
	}

	// 控制后台模式的参数，暂时不从命令行读取
	daemon_enable := true

	// 显示当前配置
	log.Printf("Configuration: serverIP=%s, remote_enable=%t", serverIP, remote_enable)

	// 执行核心业务逻辑
	latestFile, processErr := executeDownloadProcess(serverIP, remote_enable)
	// 显示处理结果
	if latestFile != "" {
		log.Printf("Latest file processed: %s", latestFile)
	} else {
		log.Println("No file was found or processed")
	}

	if processErr == nil {
		log.Println("Process completed successfully")
	} else {
		log.Println("Process failed")
		log.Printf("Error details: %v", processErr)
		os.Exit(1)
	}

	// 后台模式
	if daemon_enable {

		log.Println("Entering daemon mode...")

		// 定时查询最新的文件，如果有变更就执行核心业务逻辑
		for {

			// 查找ftp目录下所有文件并找出最新的
			newLatestFile, err := findLatestFile()
			if err != nil {
				log.Printf("Failed to find latest file: %v", err)
				// 没有找到最新的文件，大概是网络问题，需要用户介入
				// 如果没有文件，那么也就是没有频繁传递文件，不需要后台模式，先退了，等用户上传了再开启
				os.Exit(1)
			} else {
				// 比较最新文件和上一次处理的文件是否相同
				if newLatestFile != latestFile {
					log.Printf("New file detected: %s (previous: %s)", newLatestFile, latestFile)
					// 执行核心业务逻辑
					latestFile, processErr = executeDownloadProcess(serverIP, remote_enable)
					if processErr != nil {
						log.Printf("Failed to process file: %v", processErr)
						os.Exit(1)
					} else {
						log.Printf("Successfully processed new file: %s", latestFile)
						log.Println("Waiting for next check...")
					}
				}
			}
			// 定时间隔
			time.Sleep(1 * time.Minute)
		}
	}
}
